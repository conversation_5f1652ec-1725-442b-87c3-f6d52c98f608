package com.tfc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class PDFLineElementTest {

    @Test
    void testRecordCreation() {
        float x1 = 10.5f;
        float y1 = 20.7f;
        float x2 = 100.0f;
        float y2 = 50.0f;
        RGBA strokeColor = new RGBA(255, 0, 0, 255);

        PDFLineElement line = new PDFLineElement(x1, y1, x2, y2, strokeColor);

        assertEquals(x1, line.x1());
        assertEquals(y1, line.y1());
        assertEquals(x2, line.x2());
        assertEquals(y2, line.y2());
        assertSame(strokeColor, line.strokeColor());
    }

    @Test
    void testRecordWithNullStrokeColor() {
        PDFLineElement line = new PDFLineElement(0.0f, 0.0f, 10.0f, 10.0f, null);

        assertNull(line.strokeColor());
    }

    @Test
    void testRecordWithZeroCoordinates() {
        RGBA strokeColor = new RGBA(0, 255, 0, 128);
        PDFLineElement line = new PDFLineElement(0.0f, 0.0f, 0.0f, 0.0f, strokeColor);

        assertEquals(0.0f, line.x1());
        assertEquals(0.0f, line.y1());
        assertEquals(0.0f, line.x2());
        assertEquals(0.0f, line.y2());
        assertSame(strokeColor, line.strokeColor());
    }

    @Test
    void testRecordWithNegativeCoordinates() {
        RGBA strokeColor = new RGBA(0, 0, 255, 255);
        PDFLineElement line = new PDFLineElement(-10.0f, -20.0f, -5.0f, -15.0f, strokeColor);

        assertEquals(-10.0f, line.x1());
        assertEquals(-20.0f, line.y1());
        assertEquals(-5.0f, line.x2());
        assertEquals(-15.0f, line.y2());
        assertSame(strokeColor, line.strokeColor());
    }

    @Test
    void testToString() {
        RGBA strokeColor = new RGBA(128, 64, 32, 255);
        PDFLineElement line = new PDFLineElement(12.34f, 56.78f, 90.12f, 34.56f, strokeColor);

        String result = line.toString();

        assertTrue(result.contains("PDFLineElement{"));
        assertTrue(result.contains("start=[x=12.34, y=56.78]"));
        assertTrue(result.contains("end=[x=90.12, y=34.56]"));
        assertTrue(result.contains("stroke="));
    }

    @Test
    void testToStringWithNullStrokeColor() {
        PDFLineElement line = new PDFLineElement(1.0f, 2.0f, 3.0f, 4.0f, null);

        String result = line.toString();

        assertTrue(result.contains("PDFLineElement{"));
        assertTrue(result.contains("start=[x=1.00, y=2.00]"));
        assertTrue(result.contains("end=[x=3.00, y=4.00]"));
        assertTrue(result.contains("stroke=null"));
    }

    @Test
    void testToStringWithZeroCoordinates() {
        RGBA strokeColor = new RGBA(255, 255, 255, 0);
        PDFLineElement line = new PDFLineElement(0.0f, 0.0f, 0.0f, 0.0f, strokeColor);

        String result = line.toString();

        assertTrue(result.contains("start=[x=0.00, y=0.00]"));
        assertTrue(result.contains("end=[x=0.00, y=0.00]"));
    }

    @Test
    void testRecordEquality() {
        RGBA strokeColor = new RGBA(255, 0, 0, 255);

        PDFLineElement line1 = new PDFLineElement(10.0f, 20.0f, 30.0f, 40.0f, strokeColor);
        PDFLineElement line2 = new PDFLineElement(10.0f, 20.0f, 30.0f, 40.0f, strokeColor);

        assertEquals(line1, line2);
        assertEquals(line1.hashCode(), line2.hashCode());
    }

    @Test
    void testRecordInequality() {
        RGBA strokeColor = new RGBA(255, 0, 0, 255);

        PDFLineElement line1 = new PDFLineElement(10.0f, 20.0f, 30.0f, 40.0f, strokeColor);
        PDFLineElement line2 = new PDFLineElement(11.0f, 20.0f, 30.0f, 40.0f, strokeColor);

        assertNotEquals(line1, line2);
    }

    @Test
    void testRecordImmutability() {
        RGBA strokeColor = new RGBA(255, 0, 0, 255);
        PDFLineElement line = new PDFLineElement(10.0f, 20.0f, 30.0f, 40.0f, strokeColor);

        // Record fields should be final and immutable
        assertEquals(10.0f, line.x1());
        assertEquals(20.0f, line.y1());
        assertEquals(30.0f, line.x2());
        assertEquals(40.0f, line.y2());
        assertSame(strokeColor, line.strokeColor());
    }

    @Test
    void testHorizontalLine() {
        RGBA strokeColor = new RGBA(0, 0, 0, 255);
        PDFLineElement line = new PDFLineElement(10.0f, 50.0f, 100.0f, 50.0f, strokeColor);

        assertEquals(10.0f, line.x1());
        assertEquals(50.0f, line.y1());
        assertEquals(100.0f, line.x2());
        assertEquals(50.0f, line.y2());
    }

    @Test
    void testVerticalLine() {
        RGBA strokeColor = new RGBA(0, 0, 0, 255);
        PDFLineElement line = new PDFLineElement(75.0f, 10.0f, 75.0f, 100.0f, strokeColor);

        assertEquals(75.0f, line.x1());
        assertEquals(10.0f, line.y1());
        assertEquals(75.0f, line.x2());
        assertEquals(100.0f, line.y2());
    }
}
