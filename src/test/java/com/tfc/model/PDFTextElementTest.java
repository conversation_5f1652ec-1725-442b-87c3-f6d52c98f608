package com.tfc.model;

import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.Test;

import java.awt.geom.Point2D;

import static org.junit.jupiter.api.Assertions.*;

class PDFTextElementTest {

    @Test
    void testConstructorWithValidParameters() {
        String text = "Hello World";
        Point2D position = new Point2D.Float(10.0f, 20.0f);
        float fontSize = 12.0f;
        PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};
        int pageNumber = 1;

        PDFTextElement element = new PDFTextElement(text, position, fontSize, font, ctm, pageNumber);

        assertEquals(text, element.getText());
        assertEquals(fontSize, element.getFontSize());
        assertEquals(font, element.getFont());
        assertEquals(pageNumber, element.getPageNumber());
        assertArrayEquals(ctm, element.getCtm());
        
        // Position should be cloned, not the same reference
        assertNotSame(position, element.getPosition());
        assertEquals(position.getX(), element.getPosition().getX());
        assertEquals(position.getY(), element.getPosition().getY());
    }

    @Test
    void testConstructorWithNullFont() {
        String text = "Hello World";
        Point2D position = new Point2D.Float(10.0f, 20.0f);
        float fontSize = 12.0f;
        PDFont font = null;
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};
        int pageNumber = 1;

        assertThrows(NullPointerException.class, () -> {
            new PDFTextElement(text, position, fontSize, font, ctm, pageNumber);
        });
    }

    @Test
    void testGetText() {
        String text = "Test Text";
        Point2D position = new Point2D.Float(0.0f, 0.0f);
        PDFont font = new PDType1Font(Standard14Fonts.FontName.TIMES_ROMAN);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement(text, position, 10.0f, font, ctm, 0);

        assertEquals(text, element.getText());
    }

    @Test
    void testGetPosition() {
        Point2D position = new Point2D.Float(25.5f, 35.7f);
        PDFont font = new PDType1Font(Standard14Fonts.FontName.COURIER);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement("text", position, 10.0f, font, ctm, 0);

        Point2D resultPosition = element.getPosition();
        assertEquals(25.5f, resultPosition.getX(), 0.001);
        assertEquals(35.7f, resultPosition.getY(), 0.001);
        
        // Verify it's a clone, not the original
        assertNotSame(position, resultPosition);
    }

    @Test
    void testGetFontSize() {
        float fontSize = 14.5f;
        PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement("text", new Point2D.Float(0, 0), fontSize, font, ctm, 0);

        assertEquals(fontSize, element.getFontSize());
    }

    @Test
    void testGetFont() {
        PDFont font = new PDType1Font(Standard14Fonts.FontName.SYMBOL);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement("text", new Point2D.Float(0, 0), 10.0f, font, ctm, 0);

        assertSame(font, element.getFont());
    }

    @Test
    void testGetPageNumber() {
        int pageNumber = 5;
        PDFont font = new PDType1Font(Standard14Fonts.FontName.ZAPF_DINGBATS);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement("text", new Point2D.Float(0, 0), 10.0f, font, ctm, pageNumber);

        assertEquals(pageNumber, element.getPageNumber());
    }

    @Test
    void testGetCtm() {
        double[] ctm = {2.0, 0.5, -0.5, 1.5, 10.0, 20.0};
        PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);

        PDFTextElement element = new PDFTextElement("text", new Point2D.Float(0, 0), 10.0f, font, ctm, 0);

        assertArrayEquals(ctm, element.getCtm());
    }

    @Test
    void testToString() {
        String text = "Sample Text";
        Point2D position = new Point2D.Float(15.0f, 25.0f);
        float fontSize = 16.0f;
        PDFont font = new PDType1Font(Standard14Fonts.FontName.TIMES_BOLD);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};
        int pageNumber = 3;

        PDFTextElement element = new PDFTextElement(text, position, fontSize, font, ctm, pageNumber);

        String result = element.toString();
        
        assertTrue(result.contains("PDFTextElement{"));
        assertTrue(result.contains("text='Sample Text'"));
        assertTrue(result.contains("position="));
        assertTrue(result.contains("fontSize=16.0"));
        assertTrue(result.contains("font='Times-Bold'"));
        assertTrue(result.contains("pageNumber=3"));
    }

    @Test
    void testPositionCloning() {
        Point2D originalPosition = new Point2D.Float(100.0f, 200.0f);
        PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
        double[] ctm = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};

        PDFTextElement element = new PDFTextElement("text", originalPosition, 10.0f, font, ctm, 0);

        // Modify the original position
        originalPosition.setLocation(999.0, 999.0);

        // Element's position should not be affected
        assertEquals(100.0, element.getPosition().getX(), 0.001);
        assertEquals(200.0, element.getPosition().getY(), 0.001);
    }
}
